import os
import re
import pandas as pd
import numpy as np
import warnings
import knime.scripting.io as knio
warnings.simplefilter(action='ignore', category=FutureWarning)


df = knio.input_tables[0].to_pandas()
Procesed_Mtl_DF = pd.DataFrame(columns=['Style Number', 'MDM MTL Number', 'MTL WC w Greige', 'Longest MTL LT w Greige', 'MTL WC w/o Greige', 'Longest MTL LT w/o Greige',
                                      'Shell A Webcode', # New column for Shell MTL Webcode
                                      'Shell A Fabric Mill',
                                      'Shell A Dye Type',
                                      'Shell A Order Min / Order (yds)',
                                      'Shell A Order Min / Color (yds)',
                                      'Shell A Fabric Type',
                                      'Shell COO'])

# Convert lead time columns to numeric first, forcing non-numeric values to NaN, then fill with 0
df['MTL LT w/o Greige'] = pd.to_numeric(df['MTL LT w/o Greige'], errors='coerce')
df['MTL LT w Greige'] = pd.to_numeric(df['MTL LT w Greige'], errors='coerce')

# Fill NaN or empty rows with 0 for lead time columns
df['MTL LT w/o Greige'] = df['MTL LT w/o Greige'].fillna(0)
df['MTL LT w Greige'] = df['MTL LT w Greige'].fillna(0)

# Function to preprocess the Location string
def preprocess_location(location):
    if pd.isna(location):  # Check if the value is NA
        return ''  # Return an empty string for NA values
    cleaned_location = re.sub(r'[^a-z0-9]', '', location.lower())  # Remove special characters and convert to lowercase
    return cleaned_location.replace(" ", "")  # Remove spaces

# Function to safely extract item from Series
def safe_extract(series):
    if series.empty:
        return None  # or some default value
    elif len(series) == 1:
        return series.item()
    else:
        return series.iloc[0]  # Return the first item if there are multiple

# Function to safely convert values or return NaN for non-numeric strings
def safe_convert(value):
    if pd.isna(value):  # Check for NaN values
        return np.nan  # Return NaN for NaN values
    try:
        return int(value)  # Try converting to integer
    except ValueError:
        return np.nan  # Return NaN if conversion fails


####################################################################################################

for style_number in df['Style Number'].unique():
    temp_df = df.loc[df['Style Number'] == style_number].reset_index(drop=True)

    # Get longest MTL LT w greige and its relevant webcode
    max_LT_with_greige_idx = temp_df['MTL LT w Greige'].idxmax()
    longest_LT_with_greige = temp_df.loc[max_LT_with_greige_idx, 'MTL LT w Greige']
    webcode_with_greige = temp_df.loc[max_LT_with_greige_idx, 'MTL Webcode']

    # Get longest MTL LT w/o greige and its relevant webcode
    max_LT_without_greige_idx = temp_df['MTL LT w/o Greige'].idxmax()
    longest_LT_without_greige = temp_df.loc[max_LT_without_greige_idx, 'MTL LT w/o Greige']
    webcode_without_greige = temp_df.loc[max_LT_without_greige_idx, 'MTL Webcode']

    # Preprocess the Location column for filtering
    temp_df['Processed Location'] = temp_df['Location'].apply(preprocess_location)

    # Check for Shell variations in the processed Location column
    shell_mtl_wc = None  # Initialize variable to hold Shell MTL Webcode
    shell_variation_found = temp_df[temp_df['Processed Location'].str.contains(r'\bshell\b|\bshell\s*1\b|\bshell\s*one\b|\bshell\s*a\b|\bshell\s*fabric\b', na=False)] #takes shell,shell1,shellone,shella
    if not shell_variation_found.empty:
        shell_mtl_wc = shell_variation_found.iloc[0]['MTL Webcode'] # Get the first matching row's MTL Webcode for Shell variations

    # Extract additional information
    MDM_Material_num = temp_df.loc[max_LT_with_greige_idx, 'MDM Material Number']
    if shell_mtl_wc is not None:  # Check if shell_mtl_wc was found
        shell_df = temp_df[temp_df['MTL Webcode'] == shell_mtl_wc].drop_duplicates(subset=['MTL Webcode'])
        
        Shell_A_Fabric_Mill = safe_extract(shell_df['Material Supplier'])
        Shell_A_Dye_Type = safe_extract(shell_df['Dyeing Process'])
        Shell_A_MOQ = safe_extract(shell_df['Shell A Order Min / Order (yds)'])
        Shell_A_MCQ = safe_extract(shell_df['Shell A Order Min / Color (yds)'])
        Shell_A_Fabric_Type = safe_extract(shell_df['Fabric Type'])
        Shell_A_COO = safe_extract(shell_df['Country of Origin'])
    else:
        Shell_A_Fabric_Mill = np.nan  # Use np.nan for missing values
        Shell_A_Dye_Type = np.nan
        Shell_A_MOQ = np.nan
        Shell_A_MCQ = np.nan
        Shell_A_Fabric_Type = np.nan
        Shell_A_COO = np.nan

#    print(Shell_A_Dye_Type)
    new_row = pd.DataFrame({
        'Style Number': [style_number],
        'MDM MTL Number': [MDM_Material_num],
        'MTL WC w Greige': [webcode_with_greige],
        'Longest MTL LT w Greige': [longest_LT_with_greige],
        'MTL WC w/o Greige': [webcode_without_greige],
        'Longest MTL LT w/o Greige': [longest_LT_without_greige],
        
        'Shell A Webcode': [shell_mtl_wc],
        'Shell A Fabric Mill': [Shell_A_Fabric_Mill],
        'Shell A Dye Type': [Shell_A_Dye_Type],
        'Shell A Order Min / Order (yds)': [safe_convert(Shell_A_MOQ)],
        'Shell A Order Min / Color (yds)': [safe_convert(Shell_A_MCQ)],
        'Shell A Fabric Type': [Shell_A_Fabric_Type],
        'Shell COO': [Shell_A_COO]
    })
#    print(new_row)

    # Concatenate the new row to longest_LT_df
    Procesed_Mtl_DF = pd.concat([Procesed_Mtl_DF, new_row], ignore_index=True)

Procesed_Mtl_DF = Procesed_Mtl_DF.sort_values(by='Longest MTL LT w Greige', na_position='last', ascending=False).reset_index(drop=True)
print(Procesed_Mtl_DF)
knio.output_tables[0] = knio.Table.from_pandas(Procesed_Mtl_DF)