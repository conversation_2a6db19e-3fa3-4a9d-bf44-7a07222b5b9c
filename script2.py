import knime.scripting.io as knio
from datetime import datetime
import pandas as pd
import os

# Read input tables into DataFrames
df = knio.input_tables[0].to_pandas()
Dropped_SKUs = knio.input_tables[1].to_pandas()
Missing_Vendor_in_LL = knio.input_tables[2].to_pandas()
Missing_FFC_Code = knio.input_tables[3].to_pandas()
Missing_FG_Std_LT = knio.input_tables[4].to_pandas()
Bom_Changed = knio.input_tables[5].to_pandas()

# Retrieve the workflow path for saving the Excel file
var_workflow_path = knio.flow_variables['knime.workspace']
print("var_workflow_path : ", var_workflow_path)
# Get today's date in DDMMYYYY format
today_date = datetime.now().strftime("%d%m%Y_%H%M%S")  # Format date

# Update the Excel file path to include today's date
var_path_excel_file = os.path.join(var_workflow_path, f"TBL_LT_File_{today_date}.xlsx")
var_path_excel_file = var_path_excel_file.replace("\\", "/")
print("var_path_excel_file : ", var_path_excel_file)

# Create Excel formulas for the first DataFrame
df['LT1 Vessel w GB'] = [f'=IF(AB{row} <> 0, SUM(V{row}, Y{row}, AB{row}), SUM(V{row}, Y{row}, AA{row}))' for row in range(2, df.shape[0] + 2)]
df['LT1 Vessel w/o GB'] = [f'=IF(AB{row} <> 0, SUM(X{row}, Y{row}, AB{row}), SUM(X{row}, Y{row}, AA{row}))' for row in range(2, df.shape[0] + 2)]
df['Total LT1 (Without Shut Down)'] = [f'=IF(T{row}="", "Fill in Yes or No in Column T", IF(T{row} = "Yes", IF(AB{row} <> 0, SUM(V{row},Y{row},AB{row}),SUM(V{row},Y{row},AA{row})), IF(T{row}="No", IF(AB{row}<>0,SUM(X{row},Y{row},AB{row}),SUM(X{row},Y{row},AA{row})))))' for row in range(2, df.shape[0] + 2)]
#df['Total LT1 (Without Shut Down)'] = [f'=IF(T{row}="", "Fill in Yes or No in Column T", IF(T{row} = "Yes", IF(AB{row} <> 0, SUM(V{row},Y{row},AB{row}),SUM(V{row},Y{row},AA{row})), IF(T{row}="No", IF(AB{row}<>0,SUM(X{row},Y{row},AB{row}),SUM(X{row},Y{row},AA{row})))))' for row in range(2, df.shape[0] + 2)]

# Create a Pandas Excel writer using XlsxWriter as the engine.
writer = pd.ExcelWriter(var_path_excel_file, engine="xlsxwriter")

def write_dataframe_to_sheet(df, sheet_name):
    # Write the DataFrame data to XlsxWriter. Turn off the default header and index.
    df.to_excel(writer, sheet_name=sheet_name, startrow=1, header=False, index=False)
    
    # Get the xlsxwriter worksheet object for the specified sheet.
    worksheet = writer.sheets[sheet_name]
    
    # Get the dimensions of the DataFrame.
    max_row, max_col = df.shape
    
    # Create a list of column headers.
    column_settings = [{"header": column} for column in df.columns]
    
    # Add the Excel table structure.
    worksheet.add_table(0, 0, max_row, max_col - 1, {"columns": column_settings})
    
    header_format = writer.book.add_format({'bold': True, 'align': 'center', 'valign': 'vcenter', 'text_wrap': True})
    
    for col_num in range(max_col):
        worksheet.write(0, col_num, df.columns[col_num], header_format)  # Write headers
        worksheet.set_column(col_num, col_num, 15)  # Set column width to 15

    # Make the columns wider for clarity.
    worksheet.set_column(0, max_col - 1, 12)


# Write both DataFrames to their respective sheets using the function
write_dataframe_to_sheet(df, "TBL_LT_File")
write_dataframe_to_sheet(Dropped_SKUs, "Dropped_SKUs")
write_dataframe_to_sheet(Missing_Vendor_in_LL, "Missing_Vendor_in_LL")
write_dataframe_to_sheet(Missing_FFC_Code, "Missing_FFC_Code")
write_dataframe_to_sheet(Missing_FG_Std_LT, "Missing_FG_Std_LT")
write_dataframe_to_sheet(Bom_Changed, "Bom_Changed")


# Close the Pandas Excel writer and output the Excel file.
writer.close()

# Create a new DataFrame just for the Excel file path
excel_file_output_df = pd.DataFrame({'Excel File Path': [var_path_excel_file]})

# Assign this new DataFrame as an output table in KNIME
knio.output_tables[0] = knio.Table.from_pandas(excel_file_output_df)