import knime.scripting.io as knio
import pandas as pd

table1 = knio.input_tables[0].to_pandas()  # Load input Table 1 (current Sunday LL)
table2 = knio.input_tables[1].to_pandas()  # Load input Table 2 (Last LT file run)
table2[['Delta', 'Garment LT Exceptions']] = table2[['Delta', 'Garment LT Exceptions']].fillna(0)
table2[['Delta', 'Garment LT Exceptions']] = table2[['Delta', 'Garment LT Exceptions']].astype(int)
table1 = table1.loc[table1['Product Category'] == 'ACC']
table2 = table2.loc[table2['Product Category'] == 'ACC']
shutdown_days_table = knio.input_tables[2].to_pandas()

dropped_skus = table2[~table2['SCO'].isin(table1['SCO'])] # Get Dropped SKUs (SKUs in table 2 but not in table 1)
dropped_skus['Description'] = "Dropped Style" # Add new column "Description" w value: "Dropped Style"
if dropped_skus[['SCO', 'Description']].empty:
    new_row = pd.DataFrame({'SCO': ['NIL'], 'Description': ['NIL']}) # Create a DataFrame with the new row   
    dropped_skus = pd.concat([dropped_skus, new_row], ignore_index=True) # Append the new row to dropped_skus

def compare_with_nan(val1, val2):
    if pd.isna(val1) and pd.isna(val2): # If both are NaN, then equal
        return False  
    elif pd.isna(val1) or pd.isna(val2):
        return True  # If one is NaN and the other is not, then not equal
    else: # check for equality of values
        return val1 != val2  # Return True if they are not equal

def update_values(row, table2, index, table1):
    sco_value = row['SCO']
    if sco_value in table2['SCO'].values:
        for column in ['Greige Feasibility', 'Shell A C/O to Next Season', 'Transport fabric to factory (ex mill to factory) - By Vessel', 'Transit Lane', 
                        'Remark', 'Shut down days to add for 1st Buy', 'Shut down days to add for 2nd Buy','Shut down days to add for 3rd Buy',
                        'Shut down days to add for 4th Buy', 'Shut down days to add for 5th Buy', 'Shut down days to add for 6th Buy',
                        'Original First agreed buy date (after alignment meeting)', 'Conclusion', 'Remark on buy readiness', 'Target next Buy Date']:
            value = table2.loc[table2['SCO'] == sco_value, column].values[0]
            if pd.notna(value):
                table1.at[index, column] = value

for index, row in table1.iterrows():
    sco_value = row['SCO']    
    if sco_value in table2['SCO'].values: #Check if SCO exists in file of last run
        garment_lt_exceptions_value = table2.loc[table2['SCO'] == sco_value, 'Garment LT Exceptions'].values[0] #Get Garment LT Exceptions value from table2
        
        if garment_lt_exceptions_value != 0:  # Update Garment LT Exceptions only if it's not zero in table2
            table1.at[index, 'Garment LT Exceptions'] = garment_lt_exceptions_value
            delta_value = abs(row['Garment LT Exceptions'] - garment_lt_exceptions_value) # get absolute Delta when garment_lt_exceptions_value is not zero
            table1.at[index, 'Delta'] = delta_value
        else: # If garment_lt_exceptions_value is zero, dont change delta
            pass

        update_values(row, table2, index, table1)
#        if pd.notna(greige_feasibility_value): #Update greige_feasibility_value in table 1 if it is not NA in table2
#            table1.at[index, 'Greige Feasibility'] = greige_feasibility_value

        # Compare Fabric Web Codes and Shell A Webcode
        fabric_web_code_longest_lt_with_greige_1 = row['Fabric Web Code (Longest LT - With Greige)']
        fabric_web_code_longest_lt_without_greige_1 = row['Fabric Web Code (Longest LT - Without Greige)']
        shell_a_webcode_1 = row['Shell A Webcode']

        fabric_web_code_longest_lt_with_greige_2 = table2.loc[table2['SCO'] == sco_value, 'Fabric Web Code (Longest LT - With Greige)'].values[0]
        fabric_web_code_longest_lt_without_greige_2 = table2.loc[table2['SCO'] == sco_value, 'Fabric Web Code (Longest LT - Without Greige)'].values[0]
        shell_a_webcode_2 = table2.loc[table2['SCO'] == sco_value, 'Shell A Webcode'].values[0]
       
        
        change_messages = []        
        # Check for BOM changes considering NaN as a difference
        if (compare_with_nan(fabric_web_code_longest_lt_with_greige_1, fabric_web_code_longest_lt_with_greige_2)):
            change_messages.append(f"Fabric WC (Longest w GB) changed from {fabric_web_code_longest_lt_with_greige_2} to {fabric_web_code_longest_lt_with_greige_1}")
        if (compare_with_nan(fabric_web_code_longest_lt_without_greige_1, fabric_web_code_longest_lt_without_greige_2)):
            change_messages.append(f"Fabric WC (Longest w/o GB) changed from {fabric_web_code_longest_lt_without_greige_2} to {fabric_web_code_longest_lt_without_greige_1}")
        if (compare_with_nan(shell_a_webcode_1, shell_a_webcode_2)):
            change_messages.append(f"Shell A WC changed from {shell_a_webcode_1} to {shell_a_webcode_2}")
        # Set the 'BOM Changed' status and concatenate change messages if any changes were found
        if change_messages:
            table1.at[index, 'BOM Changed'] = 'Yes'
            table1.at[index, 'Change Details'] = "; ".join(change_messages)  # Store all change messages in a new column
        else:
            table1.at[index, 'BOM Changed'] = 'No'
            table1.at[index, 'Change Details'] = ""
    else:
        # If SCO doesn't exist in table2, set 'BOM Changed' to 'No' and 'Change Details' to an empty string
        table1.at[index, 'BOM Changed'] = 'No'
        table1.at[index, 'Change Details'] = ""

# Set Approval Status based on Delta value
table1['Approval Status'] = table1['Delta'].apply(lambda x: 'Yes' if x == 0 else 'No')

TBL_LT_File = table1[table1['Factory FFC'].notnull()]
TBL_LT_File = TBL_LT_File.drop(columns=['BOM Changed','Change Details'])
#TBL_LT_File = TBL_LT_File.drop(columns=[col for col in ['BOM Changed', 'Change Details'] if col in TBL_LT_File.columns])
TBL_LT_File = TBL_LT_File[TBL_LT_File['VF Office'] == 'ASO']
print(TBL_LT_File.columns)

#region: Shutdown days calculation
# Ensure the shutdown days columns are string type to avoid type conflicts
shutdown_columns = ['Shut down days to add for 1st Buy', 'Shut down days to add for 2nd Buy', 
                   'Shut down days to add for 3rd Buy', 'Shut down days to add for 4th Buy',
                   'Shut down days to add for 5th Buy', 'Shut down days to add for 6th Buy']
for col in shutdown_columns:
    if col in TBL_LT_File.columns:
        TBL_LT_File[col] = TBL_LT_File[col].astype(str)

# Perform lookup from shutdown_days_table using MDM Material Number

def lookup_shutdown_values(sco, shutdown_days_table):
    """
    Lookup values from shutdown_days_table based on MDM Material Number (using SCO)
    Returns a dictionary with the matched values or None if not found
    """
    if pd.isna(sco):
        return None
    
    # Find matching row in shutdown_days_table
    matching_rows = shutdown_days_table[shutdown_days_table['MDM Material Number'] == sco]
    
    if not matching_rows.empty:
        # Return the first matching row as a dictionary
        return matching_rows.iloc[0].to_dict()
    else:
        return None

# Create a copy to avoid SettingWithCopyWarning
TBL_LT_File = TBL_LT_File.copy()

# Apply lookup for each row in TBL_LT_File
for index, row in TBL_LT_File.iterrows():
    sco = row['SCO']
    
    # Perform lookup
    shutdown_values = lookup_shutdown_values(sco, shutdown_days_table)
    
    if shutdown_values:
        regular_value = shutdown_values["Max(Total Bulk LT Without Greige (in days, # only)*)"]
        lt_1st_buy = shutdown_values['Max(Total Bulk LT Without Greige (1st Buy)*)']
        lt_2nd_buy = shutdown_values['Max(Total Bulk LT Without Greige (2nd Buy)*)']
        lt_3rd_buy = shutdown_values['Max(Total Bulk LT Without Greige (3rd Buy)*)']
        lt_4th_buy = shutdown_values['Max(Total Bulk LT Without Greige (4th Buy)*)']
        lt_5th_buy = shutdown_values['Max(Total Bulk LT Without Greige (5th Buy)*)']
        lt_6th_buy = shutdown_values['Max(Total Bulk LT Without Greige (6th Buy)*)']

        #shutdown days = lt_buy - regular_value (if both are numbers)
        
        # Helper function to convert to numeric if possible
        def to_numeric_safe(value):
            try:
                if pd.isna(value):
                    return None
                return float(value)
            except (ValueError, TypeError):
                return None
                
        # Convert values to numeric
        regular_value_num = to_numeric_safe(regular_value)
        lt_1st_buy_num = to_numeric_safe(lt_1st_buy)
        lt_2nd_buy_num = to_numeric_safe(lt_2nd_buy)
        lt_3rd_buy_num = to_numeric_safe(lt_3rd_buy)
        lt_4th_buy_num = to_numeric_safe(lt_4th_buy)
        lt_5th_buy_num = to_numeric_safe(lt_5th_buy)
        lt_6th_buy_num = to_numeric_safe(lt_6th_buy)
        
        # Calculate shutdown days for 1st Buy
        if regular_value_num is not None and lt_1st_buy_num is not None:
            shutdown_days_1st_buy = int(lt_1st_buy_num - regular_value_num)  # Convert to integer
        else:
            shutdown_days_1st_buy = lt_1st_buy
            
        # Calculate shutdown days for 2nd Buy
        if regular_value_num is not None and lt_2nd_buy_num is not None:
            shutdown_days_2nd_buy = int(lt_2nd_buy_num - regular_value_num)  # Convert to integer
        else:
            shutdown_days_2nd_buy = lt_2nd_buy
            
        # Calculate shutdown days for 3rd Buy
        if regular_value_num is not None and lt_3rd_buy_num is not None:
            shutdown_days_3rd_buy = int(lt_3rd_buy_num - regular_value_num)  # Convert to integer
        else:
            shutdown_days_3rd_buy = lt_3rd_buy
            
        # Calculate shutdown days for 4th Buy
        if regular_value_num is not None and lt_4th_buy_num is not None:
            shutdown_days_4th_buy = int(lt_4th_buy_num - regular_value_num)  # Convert to integer
        else:
            shutdown_days_4th_buy = lt_4th_buy
            
        # Calculate shutdown days for 5th Buy
        if regular_value_num is not None and lt_5th_buy_num is not None:
            shutdown_days_5th_buy = int(lt_5th_buy_num - regular_value_num)  # Convert to integer
        else:
            shutdown_days_5th_buy = lt_5th_buy
            
        # Calculate shutdown days for 6th Buy
        if regular_value_num is not None and lt_6th_buy_num is not None:
            shutdown_days_6th_buy = int(lt_6th_buy_num - regular_value_num)  # Convert to integer
        else:
            shutdown_days_6th_buy = lt_6th_buy
            
        # Assign the calculated values to the TBL_LT_File columns
        TBL_LT_File.at[index, 'Shut down days to add for 1st Buy'] = str(shutdown_days_1st_buy) if pd.notna(shutdown_days_1st_buy) else ""
        TBL_LT_File.at[index, 'Shut down days to add for 2nd Buy'] = str(shutdown_days_2nd_buy) if pd.notna(shutdown_days_2nd_buy) else ""
        TBL_LT_File.at[index, 'Shut down days to add for 3rd Buy'] = str(shutdown_days_3rd_buy) if pd.notna(shutdown_days_3rd_buy) else ""
        TBL_LT_File.at[index, 'Shut down days to add for 4th Buy'] = str(shutdown_days_4th_buy) if pd.notna(shutdown_days_4th_buy) else ""
        TBL_LT_File.at[index, 'Shut down days to add for 5th Buy'] = str(shutdown_days_5th_buy) if pd.notna(shutdown_days_5th_buy) else ""
        TBL_LT_File.at[index, 'Shut down days to add for 6th Buy'] = str(shutdown_days_6th_buy) if pd.notna(shutdown_days_6th_buy) else ""
        

print(f"Shutdown days lookup completed for {len(TBL_LT_File)} rows.")
#endregion shutdown days calculation

knio.output_tables[0] = knio.Table.from_pandas(TBL_LT_File)  # Output for combined Material Numbers
knio.output_tables[1] = knio.Table.from_pandas(dropped_skus[['SCO', 'Description']])  # Output for dropped SKUs with Description


#################### 1516: Error tracking for missing vendor in LL ####################
missing_vendor_in_LL = table1[table1['Factory Name'].isnull()]
missing_vendor_in_LL = missing_vendor_in_LL[['Style', 'Product Sub Category', 'SCO', 'Factory Name', 'Product Development Type', 'PRO/TREE/EQ']]
knio.output_tables[2] = knio.Table.from_pandas(missing_vendor_in_LL)  # Output table for rows with empty EUR master factory u in LL


#################### 1517: Error tracking for missing Factory FFC ####################
missing_FFC_Code = table1[table1['Factory FFC'].isnull()]
missing_FFC_Code = missing_FFC_Code[['Style', 'Product Sub Category', 'SCO', 'Factory FFC', 'Factory Name']]
knio.output_tables[3] = knio.Table.from_pandas(missing_FFC_Code)  # Output table 3 for rows with empty "Factory FFC"


#################### 1555: Missing FG Std LT ####################
missing_FG_Std_LT = TBL_LT_File[TBL_LT_File['Garment Production Lead-time'].isnull()]
knio.output_tables[4] = knio.Table.from_pandas(missing_FG_Std_LT)


#################### 1507: BOM Changed ####################
Bom_Changed = table1[table1['BOM Changed'] == 'Yes']
Bom_Changed = Bom_Changed[['Style', 'Product Sub Category', 'SCO', 'BOM Changed', 'Change Details']]
knio.output_tables[5] = knio.Table.from_pandas(Bom_Changed)